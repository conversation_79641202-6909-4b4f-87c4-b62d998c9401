import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import minecraftHero from "@/assets/5.png";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Hero Section */}
      <div
        className="relative h-screen bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${minecraftHero})` }}
      >
        <div className="absolute inset-0 bg-black/70"></div>
        <div className="relative z-10 flex flex-col items-center justify-center h-full text-white text-center px-4">
          <div className="minecraft-plan-card max-w-2xl mx-auto p-12">
            <div className="minecraft-block obsidian-block mb-6 mx-auto"></div>

            <h1 className="text-8xl font-bold mb-6 text-red-400 minecraft-font drop-shadow-lg">
              404
            </h1>

            <h2 className="text-4xl font-bold mb-4 text-white minecraft-font">
              🚫 Chunk Not Found
            </h2>

            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Oops! This page seems to have been destroyed by a Creeper explosion.
              The chunk you're looking for doesn't exist in our world.
            </p>

            <div className="space-y-4">
              <Link to="/">
                <Button className="professional-button text-lg px-8 py-4">
                  🏠 Return to Spawn
                </Button>
              </Link>

              <div className="text-gray-400 text-sm">
                Lost? Try going back to the main world
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-black/80 border-t border-green-400/30 py-6">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-500">© 2024 Saracraft Hosting. Even our 404 pages are blocky! 🧱</p>
        </div>
      </footer>
    </div>
  );
};

export default NotFound;
