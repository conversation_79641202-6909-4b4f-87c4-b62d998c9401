import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import minecraftHosting from "@/assets/minecraft-hosting.jpg";

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Hero Section */}
      <div 
        className="relative h-screen bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${minecraftHosting})` }}
      >
        <div className="absolute inset-0 bg-black/60"></div>
        <div className="relative z-10 flex flex-col items-center justify-center h-full text-white text-center">
          <div className="animate-float">
            <h1 className="text-7xl font-bold mb-6 drop-shadow-lg text-white minecraft-font">
              🎮 Saracraft Hosting 🎮
            </h1>
            <p className="text-2xl mb-8 drop-shadow-md max-w-2xl text-white">
              Epic Minecraft Server Hosting for Gamers & Creators
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 mt-8">
            <Link to="/login">
              <Button className="text-lg px-8 py-4 minecraft-button">
                🔐 Player Login
              </Button>
            </Link>
            <Link to="/register">
              <Button variant="outline" className="text-lg px-8 py-4 minecraft-button-outline">
                ⭐ Join the Adventure
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-primary mb-4 minecraft-font">
            🏰 Premium Gaming Platform 🏰
          </h2>
          <p className="text-xl text-muted-foreground">
            Advanced tools for epic Minecraft adventures
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="panel-card text-center p-6 rounded-lg minecraft-card">
            <div className="text-6xl mb-4">⚡</div>
            <h3 className="text-xl font-bold text-primary mb-2">Lightning Fast</h3>
            <p className="text-muted-foreground">SSD storage & dedicated CPU power</p>
          </div>

          <div className="panel-card text-center p-6 rounded-lg minecraft-card">
            <div className="text-6xl mb-4">🛡️</div>
            <h3 className="text-xl font-bold text-primary mb-2">Protected Realms</h3>
            <p className="text-muted-foreground">DDoS protection & automatic backups</p>
          </div>

          <div className="panel-card text-center p-6 rounded-lg minecraft-card">
            <div className="text-6xl mb-4">🎛️</div>
            <h3 className="text-xl font-bold text-primary mb-2">Easy Control</h3>
            <p className="text-muted-foreground">Manage everything through web panel</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;

