import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import minecraftHero from "@/assets/5.png";

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Hero Section */}
      <div
        className="relative h-screen bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${minecraftHero})` }}
      >
        <div className="absolute inset-0 bg-black/60"></div>
        <div className="relative z-10 flex flex-col items-center justify-center h-full text-white text-center px-4">
          <div className="animate-float max-w-4xl mx-auto">
            <h1 className="text-6xl md:text-7xl font-bold mb-6 drop-shadow-lg text-white minecraft-font">
              Saracraft Hosting
            </h1>
            <p className="text-xl md:text-2xl mb-8 drop-shadow-md mx-auto text-white leading-relaxed">
              Minecraft Server Hosting
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 mt-8">
            <Link to="/login">
              <Button className="text-lg px-8 py-4 professional-button">
                🔐 Player Login
              </Button>
            </Link>
            <Link to="/register">
              <Button variant="outline" className="text-lg px-8 py-4 professional-button-outline">
                ⭐ Register
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-primary mb-4">
            🏰 Premium Gaming Platform 🏰
          </h2>
          <p className="text-xl text-muted-foreground">
            Advanced tools for epic Minecraft adventures
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center p-8 rounded-xl professional-card">
            <div className="text-6xl mb-4">⚡</div>
            <h3 className="text-xl font-bold text-primary mb-3">Lightning Fast</h3>
            <p className="text-muted-foreground leading-relaxed">SSD storage & dedicated CPU power for seamless gameplay</p>
          </div>

          <div className="text-center p-8 rounded-xl professional-card">
            <div className="text-6xl mb-4">🛡️</div>
            <h3 className="text-xl font-bold text-primary mb-3">Protected Realms</h3>
            <p className="text-muted-foreground leading-relaxed">DDoS protection & automatic backups keep your world safe</p>
          </div>

          <div className="text-center p-8 rounded-xl professional-card">
            <div className="text-6xl mb-4">🎛️</div>
            <h3 className="text-xl font-bold text-primary mb-3">Easy Control</h3>
            <p className="text-muted-foreground leading-relaxed">Manage everything through our intuitive web panel</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;

