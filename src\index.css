@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 220 20% 14%;
    --foreground: 210 40% 98%;

    --card: 220 15% 18%;
    --card-foreground: 210 40% 98%;

    --popover: 220 15% 18%;
    --popover-foreground: 210 40% 98%;

    --primary: 120 60% 50%;
    --primary-foreground: 220 20% 14%;

    --secondary: 220 15% 22%;
    --secondary-foreground: 210 40% 98%;

    --muted: 220 15% 25%;
    --muted-foreground: 215 20% 65%;

    --accent: 120 60% 50%;
    --accent-foreground: 220 20% 14%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 15% 25%;
    --input: 220 15% 22%;
    --ring: 120 60% 50%;

    /* Hosting panel gradients */
    --gradient-panel: linear-gradient(135deg, hsl(220 15% 18%), hsl(220 15% 22%));
    --gradient-success: linear-gradient(135deg, hsl(120 60% 50%), hsl(120 70% 45%));
    --gradient-dark: linear-gradient(180deg, hsl(220 20% 14%), hsl(220 25% 10%));
    
    /* Professional shadows */
    --shadow-panel: 0 4px 12px hsl(220 20% 10% / 0.5);
    --shadow-glow: 0 0 20px hsl(120 60% 50% / 0.3);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }
}

@layer components {
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  
  @keyframes pixel-bounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }

  @keyframes minecraft-glow {
    0%, 100% { box-shadow: 0 0 5px rgba(34, 197, 94, 0.5); }
    50% { box-shadow: 0 0 20px rgba(34, 197, 94, 0.8), 0 0 30px rgba(34, 197, 94, 0.6); }
  }
  
  .minecraft-font {
    font-family: 'Courier New', monospace;
    text-shadow: 2px 2px 0px rgba(0, 0, 0, 0.8);
  }

  .minecraft-button {
    @apply bg-gradient-to-r from-green-500 to-green-600 hover:from-green-400 hover:to-green-500 text-white font-bold py-3 px-6 border-2 border-green-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105;
    font-family: 'Courier New', monospace;
    text-shadow: 1px 1px 0px rgba(0, 0, 0, 0.8);
    animation: minecraft-glow 2s ease-in-out infinite;
  }

  .minecraft-button-outline {
    @apply bg-transparent hover:bg-green-500/20 text-green-400 hover:text-green-300 font-bold py-3 px-6 border-2 border-green-400 hover:border-green-300 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105;
    font-family: 'Courier New', monospace;
    text-shadow: 1px 1px 0px rgba(0, 0, 0, 0.8);
  }

  .minecraft-input {
    @apply bg-gray-800 border-2 border-gray-600 focus:border-green-400 focus:ring-2 focus:ring-green-400/20 transition-all duration-200 text-green-100 placeholder:text-gray-400;
    font-family: 'Courier New', monospace;
  }

  .minecraft-card {
    @apply border-2 border-green-400/30 shadow-lg hover:shadow-xl transition-all duration-300;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(0, 0, 0, 0.8) 100%);
    backdrop-filter: blur(10px);
  }
  
  .hosting-button {
    @apply bg-gradient-success text-primary-foreground font-semibold py-3 px-6 border border-primary/20 shadow-panel hover:shadow-glow transition-all duration-300 ease-out;
  }
  
  .hosting-button:hover {
    @apply transform scale-105 shadow-glow;
  }
  
  .hosting-input {
    @apply bg-input border border-border px-4 py-3 focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-200 text-foreground placeholder:text-muted-foreground;
  }
}
