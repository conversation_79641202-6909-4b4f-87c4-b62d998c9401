import { useState } from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import minecraftHosting from "@/assets/minecraft-hosting.jpg";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement login logic
    console.log("Login:", { email, password });
  };

  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Hero Section */}
      <div 
        className="relative h-80 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${minecraftHosting})` }}
      >
        <div className="absolute inset-0 bg-black/70"></div>
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-5xl font-bold mb-4 drop-shadow-lg minecraft-font">
              🎮 Player Portal 🎮
            </h1>
            <p className="text-lg drop-shadow-md">
              Access your gaming dashboard
            </p>
          </div>
        </div>
      </div>

      {/* Login Form */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto">
          <Card className="panel-card minecraft-card">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-bold text-primary minecraft-font">
                🔐 Login
              </CardTitle>
              <p className="text-muted-foreground">
                Enter your gaming credentials
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium text-foreground">
                    📧 Email Address
                  </label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="minecraft-input"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="password" className="text-sm font-medium text-foreground">
                    🔑 Password
                  </label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="minecraft-input"
                    required
                  />
                </div>

                <Button 
                  type="submit" 
                  className="w-full text-lg font-bold minecraft-button"
                >
                  🚀 Enter Game Panel
                </Button>
              </form>

              <div className="text-center space-y-4">
                <Link 
                  to="/forgot-password" 
                  className="text-primary hover:underline text-sm"
                >
                  🤔 Forgot your password?
                </Link>
                
                <div className="border-t border-border pt-4">
                  <p className="text-muted-foreground text-sm">
                    New to Saracraft?
                  </p>
                  <Link to="/register">
                    <Button variant="outline" className="w-full mt-2 minecraft-button-outline">
                      ⭐ Create Account
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Login;
