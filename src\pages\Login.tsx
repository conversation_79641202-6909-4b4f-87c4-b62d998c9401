import { useState } from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import minecraftHero from "@/assets/5.png";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement login logic
    console.log("Login:", { email, password });
  };

  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Hero Section */}
      <div
        className="relative h-80 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${minecraftHero})` }}
      >
        <div className="absolute inset-0 bg-black/60"></div>
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-5xl font-bold mb-4 drop-shadow-lg minecraft-font">
              PLAYER PORTAL
            </h1>
            <div className="text-green-400 text-lg font-bold mb-2 minecraft-font">
              ACCESS YOUR SERVER DASHBOARD
            </div>
            <p className="text-gray-300 drop-shadow-md">
              Continue your adventure where you left off
            </p>
          </div>
        </div>
      </div>

      {/* Login Form */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto">
          <Card className="minecraft-experience-card">
            <CardHeader className="text-center pb-8">
              <CardTitle className="text-4xl font-bold text-white minecraft-font mb-4">
                LOGIN TO CONTINUE
              </CardTitle>
              <div className="text-green-400 font-bold minecraft-font mb-2">
                YOUR QUEST AWAITS
              </div>
              <p className="text-gray-300">
                Enter your credentials to access your server dashboard
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-bold text-white minecraft-font">
                    EMAIL ADDRESS
                  </label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="minecraft-experience-input"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="password" className="text-sm font-bold text-white minecraft-font">
                    PASSWORD
                  </label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="minecraft-experience-input"
                    required
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full text-lg font-bold minecraft-experience-button"
                >
                  ENTER SERVER PANEL
                </Button>
              </form>

              <div className="text-center space-y-4">
                <Link
                  to="/forgot-password"
                  className="text-green-400 hover:text-green-300 hover:underline text-sm transition-colors font-bold"
                >
                  FORGOT PASSWORD?
                </Link>

                <div className="border-t border-green-400/20 pt-6">
                  <p className="text-gray-300 text-sm mb-3 minecraft-font">
                    NEW TO SARACRAFT?
                  </p>
                  <Link to="/register">
                    <Button variant="outline" className="w-full minecraft-experience-button-outline">
                      CREATE ACCOUNT
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-black/80 border-t border-green-400/30 py-6">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-500">© 2024 Saracraft Hosting. Secure login portal 🔐</p>
        </div>
      </footer>
    </div>
  );
};

export default Login;
