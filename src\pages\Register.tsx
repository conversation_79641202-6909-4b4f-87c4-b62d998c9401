import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import minecraftHero from "@/assets/5.png";

const Register = () => {
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    agreedToTerms: false,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.password !== formData.confirmPassword) {
      alert("Passwords don't match!");
      return;
    }
    if (!formData.agreedToTerms) {
      alert("You must agree to the terms!");
      return;
    }
    // TODO: Implement registration logic
    console.log("Register:", formData);
  };

  const updateFormData = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Hero Section */}
      <div
        className="relative h-72 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${minecraftHero})` }}
      >
        <div className="absolute inset-0 bg-black/60"></div>
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-5xl font-bold mb-4 drop-shadow-lg minecraft-font">
              JOIN SARACRAFT
            </h1>
            <div className="text-green-400 text-lg font-bold mb-2 minecraft-font">
              BEGIN YOUR EPIC ADVENTURE
            </div>
            <p className="text-gray-300 drop-shadow-md">
              Create your account and start building your world
            </p>
          </div>
        </div>
      </div>

      {/* Registration Form */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto">
          <Card className="minecraft-experience-card">
            <CardHeader className="text-center pb-8">
              <CardTitle className="text-4xl font-bold text-white minecraft-font mb-4">
                CREATE ACCOUNT
              </CardTitle>
              <div className="text-green-400 font-bold minecraft-font mb-2">
                START YOUR QUEST
              </div>
              <p className="text-gray-300">
                Join thousands of players in the Saracraft community
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <label htmlFor="username" className="text-sm font-bold text-white minecraft-font">
                    PLAYER NAME
                  </label>
                  <Input
                    id="username"
                    type="text"
                    placeholder="CoolGamer123"
                    value={formData.username}
                    onChange={(e) => updateFormData("username", e.target.value)}
                    className="minecraft-experience-input"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-bold text-white minecraft-font">
                    EMAIL ADDRESS
                  </label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => updateFormData("email", e.target.value)}
                    className="minecraft-experience-input"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="password" className="text-sm font-bold text-white minecraft-font">
                    PASSWORD
                  </label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={formData.password}
                    onChange={(e) => updateFormData("password", e.target.value)}
                    className="minecraft-experience-input"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="confirmPassword" className="text-sm font-bold text-white minecraft-font">
                    CONFIRM PASSWORD
                  </label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    placeholder="••••••••"
                    value={formData.confirmPassword}
                    onChange={(e) => updateFormData("confirmPassword", e.target.value)}
                    className="minecraft-experience-input"
                    required
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="terms"
                    checked={formData.agreedToTerms}
                    onCheckedChange={(checked) => updateFormData("agreedToTerms", checked === true)}
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm text-white leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    I agree to the{" "}
                    <Link to="/terms" className="text-green-400 hover:text-green-300 hover:underline transition-colors">
                      terms & conditions
                    </Link>
                  </label>
                </div>

                <Button
                  type="submit"
                  className="w-full text-lg font-bold minecraft-experience-button"
                >
                  START ADVENTURE
                </Button>
              </form>

              <div className="text-center border-t border-green-400/20 pt-6">
                <p className="text-gray-300 text-sm mb-3 minecraft-font">
                  ALREADY HAVE AN ACCOUNT?
                </p>
                <Link to="/login">
                  <Button variant="outline" className="w-full minecraft-experience-button-outline">
                    LOGIN HERE
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-black/80 border-t border-green-400/30 py-6">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-500">© 2024 Saracraft Hosting. Join our community today! 🎮</p>
        </div>
      </footer>
    </div>
  );
};

export default Register;
