import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import minecraftHosting from "@/assets/minecraft-hosting.jpg";

const Register = () => {
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    agreedToTerms: false,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.password !== formData.confirmPassword) {
      alert("Passwords don't match!");
      return;
    }
    if (!formData.agreedToTerms) {
      alert("You must agree to the terms!");
      return;
    }
    // TODO: Implement registration logic
    console.log("Register:", formData);
  };

  const updateFormData = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen bg-gradient-dark">
      {/* Hero Section */}
      <div 
        className="relative h-72 bg-cover bg-center bg-no-repeat"
        style={{ backgroundImage: `url(${minecraftHosting})` }}
      >
        <div className="absolute inset-0 bg-black/70"></div>
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-4xl font-bold mb-4 drop-shadow-lg minecraft-font">
              ⭐ Join Saracraft ⭐
            </h1>
            <p className="text-lg drop-shadow-md">
              Start your epic gaming journey
            </p>
          </div>
        </div>
      </div>

      {/* Registration Form */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto">
          <Card className="panel-card minecraft-card">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-bold text-primary minecraft-font">
                🎮 Create Account
              </CardTitle>
              <p className="text-muted-foreground">
                Join the Saracraft community
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <label htmlFor="username" className="text-sm font-medium text-foreground">
                    👤 Player Name
                  </label>
                  <Input
                    id="username"
                    type="text"
                    placeholder="CoolGamer123"
                    value={formData.username}
                    onChange={(e) => updateFormData("username", e.target.value)}
                    className="minecraft-input"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium text-foreground">
                    📧 Email Address
                  </label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => updateFormData("email", e.target.value)}
                    className="minecraft-input"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <label htmlFor="password" className="text-sm font-medium text-foreground">
                    🔑 Password
                  </label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={formData.password}
                    onChange={(e) => updateFormData("password", e.target.value)}
                    className="minecraft-input"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="confirmPassword" className="text-sm font-medium text-foreground">
                    🔐 Confirm Password
                  </label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    placeholder="••••••••"
                    value={formData.confirmPassword}
                    onChange={(e) => updateFormData("confirmPassword", e.target.value)}
                    className="minecraft-input"
                    required
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="terms"
                    checked={formData.agreedToTerms}
                    onCheckedChange={(checked) => updateFormData("agreedToTerms", checked === true)}
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm text-foreground leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    I agree to the{" "}
                    <Link to="/terms" className="text-primary hover:underline">
                      terms & conditions
                    </Link>
                  </label>
                </div>

                <Button 
                  type="submit" 
                  className="w-full text-lg font-bold minecraft-button"
                >
                  🚀 Start Adventure
                </Button>
              </form>

              <div className="text-center border-t border-border pt-4">
                <p className="text-muted-foreground text-sm">
                  Already have an account?
                </p>
                <Link to="/login">
                  <Button variant="outline" className="w-full mt-2 minecraft-button-outline">
                    🔐 Login Here
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Register;
